<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Flickering Fix Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .chart-container { width: 400px; height: 300px; margin: 20px; display: inline-block; }
        .controls { margin: 20px 0; }
        button { margin: 5px; padding: 10px 15px; }
        .test-section { border: 1px solid #ccc; padding: 20px; margin: 20px 0; }
        h2 { color: #333; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chart Flickering Fix Test</h1>
        
        <div class="status success">
            <strong>✅ Fix Applied:</strong> Chart instances are now properly managed with destroy/create cycle
        </div>

        <div class="test-section">
            <h2>Test: Rapid Institute Selection Changes</h2>
            <p>This test simulates the flickering issue by rapidly changing chart data (similar to changing institute selection)</p>
            
            <div class="controls">
                <button onclick="simulateInstituteChange('Institute A')">Select Institute A</button>
                <button onclick="simulateInstituteChange('Institute B')">Select Institute B</button>
                <button onclick="simulateInstituteChange('Institute C')">Select Institute C</button>
                <button onclick="simulateRapidChanges()">Simulate Rapid Changes</button>
            </div>

            <div class="chart-container">
                <h3>Staff Gender Distribution</h3>
                <canvas id="chartjs-staff-gender-distribution"></canvas>
            </div>

            <div class="chart-container">
                <h3>Fee Collection Payment Mode</h3>
                <canvas id="chartjs-fee-collection-payment-mode-pie"></canvas>
            </div>

            <div class="chart-container">
                <h3>Fee Collection Fee Head Distribution</h3>
                <canvas id="chartjs-fee-collection-fee-head-distribution"></canvas>
            </div>
        </div>

        <div class="test-section">
            <h2>Chart Instance Management</h2>
            <p>Current chart instances: <span id="chart-count">0</span></p>
            <button onclick="showChartInstances()">Show Chart Instances</button>
            <button onclick="destroyAllCharts()">Destroy All Charts</button>
            <div id="chart-instances-info"></div>
        </div>
    </div>

    <script>
        // Simulate the fixed homePageV2 object
        var homePageV2 = {
            chartInstances: {},

            destroyChart: function(chartId) {
                if (homePageV2.chartInstances[chartId]) {
                    homePageV2.chartInstances[chartId].destroy();
                    delete homePageV2.chartInstances[chartId];
                    console.log('Destroyed chart:', chartId);
                }
            },

            destroyAllCharts: function() {
                Object.keys(homePageV2.chartInstances).forEach(function(chartId) {
                    homePageV2.destroyChart(chartId);
                });
                updateChartCount();
            },

            renderStaffGenderDistributionChart: function(labelArr, maleDataArr, femaleDataArr) {
                homePageV2.destroyChart('staff-gender-distribution');
                
                homePageV2.chartInstances['staff-gender-distribution'] = new Chart($("#chartjs-staff-gender-distribution"), {
                    type: "bar",
                    data: {
                        labels: labelArr,
                        datasets: [{
                            label: "Male",
                            backgroundColor: "#28a745",
                            data: maleDataArr
                        }, {
                            label: "Female", 
                            backgroundColor: "#ffc107",
                            data: femaleDataArr
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        legend: { display: true }
                    }
                });
                updateChartCount();
            },

            renderFeePaymentModeCollectionPieChart: function(labelArr, dataArr, totalAmount) {
                homePageV2.destroyChart('fee-collection-payment-mode-pie');
                
                homePageV2.chartInstances['fee-collection-payment-mode-pie'] = new Chart($("#chartjs-fee-collection-payment-mode-pie"), {
                    type: "pie",
                    data: {
                        labels: labelArr,
                        datasets: [{
                            data: dataArr,
                            backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9"]
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        legend: { display: true }
                    }
                });
                updateChartCount();
            },

            renderFeeCollectionFeeHeadDistributionChart: function(labelArr, feeHeadDatasetArr) {
                homePageV2.destroyChart('fee-collection-fee-head-distribution');
                
                homePageV2.chartInstances['fee-collection-fee-head-distribution'] = new Chart($("#chartjs-fee-collection-fee-head-distribution"), {
                    type: "horizontalBar",
                    data: {
                        labels: labelArr,
                        datasets: feeHeadDatasetArr
                    },
                    options: {
                        maintainAspectRatio: false,
                        legend: { display: true }
                    }
                });
                updateChartCount();
            }
        };

        // Test data for different institutes
        var testData = {
            'Institute A': {
                staff: { labels: ['Inst A'], male: [15], female: [20] },
                payment: { labels: ['Cash', 'Online'], data: [5000, 8000] },
                feeHead: { labels: ['Inst A'], datasets: [{ label: 'Tuition', data: [10000], backgroundColor: '#28a745' }] }
            },
            'Institute B': {
                staff: { labels: ['Inst B'], male: [25], female: [18] },
                payment: { labels: ['Cash', 'Online', 'Cheque'], data: [3000, 12000, 2000] },
                feeHead: { labels: ['Inst B'], datasets: [{ label: 'Tuition', data: [15000], backgroundColor: '#dc3545' }] }
            },
            'Institute C': {
                staff: { labels: ['Inst C'], male: [30], female: [25] },
                payment: { labels: ['Online', 'UPI'], data: [18000, 7000] },
                feeHead: { labels: ['Inst C'], datasets: [{ label: 'Tuition', data: [20000], backgroundColor: '#007bff' }] }
            }
        };

        function simulateInstituteChange(institute) {
            console.log('Simulating institute change to:', institute);
            var data = testData[institute];
            
            // Simulate the fixed chart rendering
            homePageV2.renderStaffGenderDistributionChart(data.staff.labels, data.staff.male, data.staff.female);
            homePageV2.renderFeePaymentModeCollectionPieChart(data.payment.labels, data.payment.data, 'Total');
            homePageV2.renderFeeCollectionFeeHeadDistributionChart(data.feeHead.labels, data.feeHead.datasets);
        }

        function simulateRapidChanges() {
            var institutes = ['Institute A', 'Institute B', 'Institute C'];
            var index = 0;
            
            var interval = setInterval(function() {
                simulateInstituteChange(institutes[index]);
                index = (index + 1) % institutes.length;
                
                if (index === 0) {
                    clearInterval(interval);
                    console.log('Rapid changes simulation completed');
                }
            }, 500);
        }

        function updateChartCount() {
            document.getElementById('chart-count').textContent = Object.keys(homePageV2.chartInstances).length;
        }

        function showChartInstances() {
            var info = document.getElementById('chart-instances-info');
            var instances = Object.keys(homePageV2.chartInstances);
            info.innerHTML = '<h4>Active Chart Instances:</h4><ul>' + 
                instances.map(id => '<li>' + id + '</li>').join('') + '</ul>';
        }

        function destroyAllCharts() {
            homePageV2.destroyAllCharts();
            document.getElementById('chart-instances-info').innerHTML = '<p>All charts destroyed</p>';
        }

        // Initialize with Institute A data
        window.onload = function() {
            simulateInstituteChange('Institute A');
        };
    </script>
</body>
</html>
