{% load json %}
<style>
.select2-container {
    width: 100% !important;
}
.select2-container .select2-selection--multiple {
    width: 100% !important;
    max-width: 100% !important;
}
</style>

<div class="row align-items-center justify-content-end">
    <!-- Institute Selection -->
    <div class="col-md-3 pr-2">
        <select class="form-control select2" multiple data-toggle="select2" id="select-institutes" data-style="bg-white border" title="Select Attendance Type" style="width: 100% !important;">
            {% for institute in institutes %}
                <option value={{institute.institute_id}}>{{institute.branch_name}}</option>
            {% endfor%}
        </select>
    </div>

    <!-- Date Selection -->
    <div class="col-md-3 px-2">
        <input type="text" class="select-date mandatory-field form-control bg-white shadow-sm" id="stats-date-range" value="{{date}}" style="width: 100% !important;"/>
    </div>

    <!-- Refresh Button -->
    <div class="col-md-1 pl-2">
        <button class="btn btn-primary" onclick="homePageV2.loadWidgets()">
            <i class="align-middle" data-feather="refresh-cw"></i> &nbsp; Apply
        </button>
    </div>
</div>
<br>

<div id="attendance-dashboard-session-content">
    {% include 'organisation_portal/layouts/v2/dashboard-date-range-content.html' %}
</div>
