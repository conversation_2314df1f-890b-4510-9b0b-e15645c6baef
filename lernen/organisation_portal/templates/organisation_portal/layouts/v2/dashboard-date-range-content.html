{% load json %}
{% load currency_utils %}
{% load displaytime %}
{% load displayPercentage %}
{% load round_to_decimals %}
{% load math_utils %}
{% load string_utils %}
<style>
.stats-widget-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

/*
=== VIBRANT LIGHT CHART COLORS ===
Theme Base: #43a2ad

=== STUDENT DISTRIBUTION CHART COLORS ===
Light vibrant variations of the theme for pie charts
*/
:root {
    /* Student Distribution - Light vibrant theme variations */
    --student-chart-color-1: #7bc4ce;  /* Light vibrant primary */
    --student-chart-color-2: #9dd4dc;  /* Lighter vibrant */
    --student-chart-color-3: #bfe4ea;  /* Very light vibrant */
    --student-chart-color-4: #d4eef2;  /* Pale vibrant */
    --student-chart-color-5: #e8f7f9;  /* Ultra light vibrant */
    --student-chart-color-6: #f2fbfc;  /* Whisper light */

    /* Fee Payment Distribution - Light warm variations */
    --fee-payment-color-1: #6bb8c4;   /* Light teal-blue */
    --fee-payment-color-2: #8cc8d2;   /* Soft aqua */
    --fee-payment-color-3: #add8e0;   /* Light aqua */
    --fee-payment-color-4: #cee8ee;   /* Pale aqua */
    --fee-payment-color-5: #e6f4f7;   /* Very light aqua */
    --fee-payment-color-6: #f0f9fb;   /* Whisper aqua */

    /* Gender Distribution - Light complementary colors */
    --gender-male-color: #5cb3bf;     /* Light vibrant male */
    --gender-female-color: #f4a6cd;   /* Light vibrant pink */

    /* Fee Head Distribution - Dynamic light palette */
    --fee-head-color-1: #7bc4ce;      /* Primary light */
    --fee-head-color-2: #a8d5a8;      /* Light green */
    --fee-head-color-3: #f4c2a1;      /* Light orange */
    --fee-head-color-4: #d4a5d4;      /* Light purple */
    --fee-head-color-5: #f4d03f;      /* Light yellow */
    --fee-head-color-6: #85c1e9;      /* Light blue */
    --fee-head-color-7: #f1948a;      /* Light coral */
    --fee-head-color-8: #aed6f1;      /* Light sky */
    --fee-head-color-9: #d5dbdb;      /* Light gray */
    --fee-head-color-10: #f8c471;     /* Light amber */
}

/* === DRAG AND DROP STYLES === */
.dashboard-container {
    min-height: 100vh;
}

.widget-container {
    position: relative;
    transition: all 0.3s ease;
}

.widget-container.ui-sortable-helper {
    transform: rotate(5deg);
    box-shadow: 0 8px 25px rgba(67, 162, 173, 0.3);
    z-index: 1000;
}

.widget-container.ui-sortable-placeholder {
    border: 2px dashed #43a2ad;
    background-color: rgba(67, 162, 173, 0.1);
    visibility: visible !important;
    height: auto !important;
    min-height: 100px;
}

.widget-container .drag-handle {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: move;
    color: #43a2ad;
    font-size: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 4px 6px;
    border: 1px solid #e0e0e0;
}

.widget-container:hover .drag-handle {
    opacity: 1;
}

.widget-container.ui-sortable-helper .drag-handle {
    opacity: 1;
}

.sortable-row {
    min-height: 50px;
}

.drop-zone-indicator {
    border: 2px dashed #43a2ad;
    background-color: rgba(67, 162, 173, 0.1);
    min-height: 80px;
    margin: 10px 0;
    border-radius: 8px;
    display: none;
    align-items: center;
    justify-content: center;
    color: #43a2ad;
    font-weight: 500;
}

.drop-zone-indicator.active {
    display: flex;
}

/* Drag feedback */
.ui-sortable-helper .card {
    border: 2px solid #43a2ad;
    background-color: rgba(255, 255, 255, 0.95);
}

/* Reset button styles */
.dashboard-controls {
    position: fixed;
    top: 60px;
    right: 20px;
    z-index: 1000;
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.reset-layout-btn {
    background-color: #43a2ad;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.reset-layout-btn:hover {
    background-color: #3a8f99;
}
</style>
<!-- Dashboard Controls -->
<div class="dashboard-controls">
    <button class="reset-layout-btn" onclick="dashboardDragDrop.resetLayout()">
        <i class="fas fa-undo"></i> Reset Layout
    </button>
</div>

<p style="display:none;" id="home-page-stats">{{institute_details_map|jsonstr}}</p>
<div class="dashboard-container">
    <div class="row sortable-row" id="stats-row">
        <div class="col-12 col-sm-6 col-xxl d-flex widget-container" data-widget-id="student-stats">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill">
                <div class="card-body py-4">
                    <div class="media">
                        <div class="media-body">
                            <div class="student-count-stats">
                                {% include 'core/utils/widget_loader.html' %}
                                <h3 class="mb-2 stats-widget-content"></h3>
                                <p class="mb-2">Enrolled Students</p>
                            </div>
                            <div class="student-admission-stats">
                                <!-- {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %} -->
                                <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                                <span class="badge badge-soft-success" >
                                    <i class="mdi mdi-arrow-bottom-right"></i>
                                    <span class="stats-widget-content-admission"></span>
                                  </span>
                                  <span class="text-muted">Admissions</span>
                                  &nbsp;
                                  <span class="badge badge-soft-danger" >
                                    <i class="mdi mdi-arrow-bottom-right"></i>
                                    <span class="stats-widget-content-tc"></span>
                                  </span>
                                  <span class="text-muted">TC Issued</span>
                                </div>
                            </div>
                        </div>
                        <div class="d-inline-block ml-3">
                            <div class="stat">
                              <i class="align-middle text-success" data-feather="users"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xxl d-flex widget-container" data-widget-id="staff-stats">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill">
                <div class="card-body py-4">
                    <div class="media">
                        <div class="media-body">
                            <div class="staff-count-stats">
                                {% include 'core/utils/widget_loader.html' %}
                                <h3 class="mb-2 stats-widget-content"></h3>
                                <p class="mb-2">Total Staff</p>
                            </div>
                            <div class="staff-attendance-stats">
                                <!-- {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %} -->
                                <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                                  <span class="badge badge-soft-success" >
                                    <i class="mdi mdi-arrow-bottom-right"></i>
                                    <span class="stats-widget-content-staff-present"></span>
                                  </span>
                                  <span class="text-muted">Present</span>
                                  &nbsp;
                                  <span class="badge badge-soft-danger" >
                                    <i class="mdi mdi-arrow-bottom-right"></i>
                                    <span class="stats-widget-content-staff-leave"></span>
                                  </span>
                                  <span class="text-muted">Leave</span>
                                </div>
                            </div>
                        </div>
                        <div class="d-inline-block ml-3">
                            <div class="stat">
                              <i class="align-middle text-success" data-feather="users"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xxl d-flex widget-container student-attendance-stats" data-widget-id="student-attendance">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill">
                {% include 'core/utils/widget_loader.html' %}
                <div class="stats-widget-content"></div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xxl d-flex widget-container fee-collection-stats" data-widget-id="fees-stats">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill">
                <div class="card-body py-4">
                    <div class="media">
                        <div class="media-body">
                            <div>
                                {% include 'core/utils/widget_loader.html' %}
                                <div>
                                  <h3 class="mb-2"><i class="align-middle me-2 fas fa-fw fa-rupee-sign"></i>
                                    <span class="mb-2 stat-value"></span>
                                  </h3>
                                  <p class="mb-2">Total Fees Collection</p>
                                </div>
                            </div>
                        </div>
                        <div class="d-inline-block ml-3">
                            <div class="stat">
                              <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row sortable-row" id="charts-row-1">
        <div class="col-6 col-lg-3 d-flex widget-container student-count-distribution-stats" data-widget-id="student-distribution">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100" style="height: 450px;">
                <div class="card-header">
                  <h5 class="card-title mb-0">Student Distribution</h5>
                </div>
                <div style="overflow-y: auto; flex: 1; position: relative;">
                    {% include 'core/utils/widget_loader.html' %}
                    <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                        <div class="py-3">
                            <div class="chart chart-xs">
                                <canvas id="chartjs-student-count-pie"></canvas>
                            </div>
                        </div>
                        <div class="student-institute-count-table">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-lg-5 d-flex widget-container staff-gender-distribution-stats" data-widget-id="staff-gender">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100" style="height: 450px;">
                <div class="card-header">
                  <h5 class="card-title mb-0">Staff Gender Distribution</h5>
                </div>
                <br/>
                <div style="overflow-y: auto; flex: 1; position: relative;">
                    {% include 'core/utils/widget_loader.html' %}
                    <div class="align-self-center chart chart-lg stats-widget-content" style="display: none;">
                        <canvas id="chartjs-staff-gender-distribution"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-lg-4 d-flex widget-container fee-collection-payment-mode-distribution-stats" data-widget-id="payment-mode">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100" style="height: 450px;">
                <div class="card-header">
                  <h5 class="card-title mb-0">Fee Collection By Payment Mode</h5>
                </div>
                <div style="overflow-y: auto; flex: 1; position: relative;">
                    {% include 'core/utils/widget_loader.html' %}
                    <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                        <div class="py-3">
                            <div class="chart chart-xs">
                                <canvas id="chartjs-fee-collection-payment-mode-pie"></canvas>
                            </div>
                        </div>
                        <div class="fee-collection-institute-table">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row sortable-row" id="charts-row-2">
        <div class="col-12 col-lg-3 d-flex widget-container" data-widget-id="student-birthdays">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100 pl-0 pr-0">
                <div class="card-header">
                  <h5 class="card-title mb-0" id="new-admission-graph-header-text">Student Birthdays</h5>
              </div>
      <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-after-enrollment-structure-tab" data-toggle="tab" href="#nav-after-enrollment-structure" role="tab" aria-controls="nav-home" aria-selected="true">Today</a>
          <a class="nav-item nav-link" id="nav-before-registration-structure-tab" data-toggle="tab" href="#before-registration-structure" role="tab" aria-controls="nav-profile" aria-selected="false">Upcoming</a>
        </div>
      </nav>
      <br/>
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-after-enrollment-structure" role="tabpanel" aria-labelledby="nav-after-enrollment-structure-tab">
              <div style="display:block;overflow-y: auto;max-height: 385px;">
              {% for institute_unique_key, institute_stats in institute_details_map.items %}
              {% if institute_stats.today_birthday %}
              <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
              <div class="d-flex">
                <div class="w-100">
                  <div class="mb-0">
                    {% for student in institute_stats.today_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                    </div>
                  </div>
                </div>
              {% endif %}
              {% endfor %}
              </div>
          </div>
          <div class="tab-pane fade" id="before-registration-structure" role="tabpanel" aria-labelledby="nav-before-registration-structure-tab">
            <div style="display:block;overflow-y: auto;max-height: 385px;">
            {% for institute_unique_key, institute_stats in institute_details_map.items %}
            {% if institute_stats.upcoming_birthday %}
            <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
            <div class="d-flex">
              <div class="w-100">
                <div class="mb-0">
                    {% for student in institute_stats.upcoming_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
          </div>
          {% endif %}
          {% endfor %}
          </div>
        </div>
      </div>
        </div>
        </div>
        <div class="col-12 col-lg-3 d-flex widget-container" data-widget-id="transport-students">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100">
                <div class="card-header">
                  <h5 class="card-title mb-0">Active Transport Students</h5>
                </div>
                <div class="card-body d-flex" style="position: relative;">
                    {% include 'core/utils/widget_loader.html' %}
                    <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                        <div class="py-3">
                            <div class="chart chart-xs">
                              <canvas id="chartjs-active-transport-pie"></canvas>
                            </div>
                        </div>
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Branches</th>
                                    <th class="text-center">Transport/Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for institute_unique_key, institute_stats in institute_details_map.items %}
                                <tr>
                                    <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                                    <td class="text-center">{% if institute_stats.transport_stats %}{{institute_stats.transport_stats.totalTransportAssignedStudentCount}}/{{institute_stats.transport_stats.totalEnrolledStudentCount}}{% else %}-{% endif %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-6 d-flex widget-container fee-collection-fee-head-distribution-stats" data-widget-id="fee-distribution">
            <div class="drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="card flex-fill w-100">
                <div class="card-header">
                  <h5 class="card-title mb-0">Fee Collection Distribution</h5>
                </div>
                <div style="position: relative;">
                    {% include 'core/utils/widget_loader.html' %}
                    <div class="align-self-center chart chart-lg stats-widget-content" style="display: none;">
                        <canvas id="chartjs-fee-collection-fee-head-distribution"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Load SortableJS from CDN -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
// Dashboard Drag and Drop Functionality
var dashboardDragDrop = {

    // Default layout configuration
    defaultLayout: {
        'stats-row': ['student-stats', 'staff-stats', 'student-attendance', 'fees-stats'],
        'charts-row-1': ['student-distribution', 'staff-gender', 'payment-mode'],
        'charts-row-2': ['student-birthdays', 'transport-students', 'fee-distribution']
    },

    sortableInstances: [],

    // Initialize drag and drop functionality
    init: function() {
        this.loadSavedLayout();
        this.initializeSortable();
        this.bindEvents();
    },

    // Initialize SortableJS
    initializeSortable: function() {
        var self = this;

        // Clear existing instances
        this.sortableInstances.forEach(function(instance) {
            instance.destroy();
        });
        this.sortableInstances = [];

        // Make each row sortable
        document.querySelectorAll('.sortable-row').forEach(function(row) {
            var sortable = Sortable.create(row, {
                group: 'dashboard-widgets',
                handle: '.drag-handle',
                animation: 300,
                ghostClass: 'ui-sortable-placeholder',
                chosenClass: 'ui-sortable-helper',
                dragClass: 'ui-sortable-helper',
                filter: 'input,textarea,button,select,option,canvas,a,.no-drag',
                preventOnFilter: false,

                onStart: function(evt) {
                    evt.item.classList.add('ui-sortable-helper');
                    document.querySelectorAll('.drop-zone-indicator').forEach(function(el) {
                        el.classList.add('active');
                    });
                },

                onEnd: function(evt) {
                    evt.item.classList.remove('ui-sortable-helper');
                    document.querySelectorAll('.drop-zone-indicator').forEach(function(el) {
                        el.classList.remove('active');
                    });
                    self.saveLayout();
                },

                onMove: function(evt) {
                    // Prevent dragging into non-widget elements
                    return evt.related.classList.contains('widget-container') ||
                           evt.related.classList.contains('sortable-row');
                }
            });

            self.sortableInstances.push(sortable);
        });
    },

    // Bind additional events
    bindEvents: function() {
        var self = this;

        // Show drag handles on hover using vanilla JavaScript
        document.querySelectorAll('.widget-container').forEach(function(container) {
            container.addEventListener('mouseenter', function() {
                var handle = this.querySelector('.drag-handle');
                if (handle) {
                    handle.style.opacity = '1';
                    handle.style.transition = 'opacity 0.2s ease';
                }
            });

            container.addEventListener('mouseleave', function() {
                var handle = this.querySelector('.drag-handle');
                if (handle) {
                    handle.style.opacity = '0';
                    handle.style.transition = 'opacity 0.2s ease';
                }
            });
        });

        // Prevent drag when clicking on interactive elements
        document.querySelectorAll('.widget-container').forEach(function(container) {
            container.addEventListener('mousedown', function(e) {
                var target = e.target;
                var interactiveElements = ['CANVAS', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA', 'A'];

                if (interactiveElements.includes(target.tagName) ||
                    target.closest('canvas, button, input, select, textarea, a')) {
                    e.stopPropagation();
                }
            });
        });
    },

    // Save current layout to localStorage
    saveLayout: function() {
        var layout = {};

        document.querySelectorAll('.sortable-row').forEach(function(row) {
            var rowId = row.getAttribute('id');
            var widgets = [];

            row.querySelectorAll('.widget-container').forEach(function(widget) {
                var widgetId = widget.getAttribute('data-widget-id');
                if (widgetId) {
                    widgets.push(widgetId);
                }
            });

            layout[rowId] = widgets;
        });

        localStorage.setItem('dashboard-layout', JSON.stringify(layout));
        console.log('Dashboard layout saved:', layout);
    },

    // Load saved layout from localStorage
    loadSavedLayout: function() {
        var savedLayout = localStorage.getItem('dashboard-layout');

        if (savedLayout) {
            try {
                var layout = JSON.parse(savedLayout);
                this.applyLayout(layout);
                console.log('Dashboard layout loaded:', layout);
            } catch (e) {
                console.error('Error loading saved layout:', e);
                this.resetLayout();
            }
        }
    },

    // Apply a specific layout
    applyLayout: function(layout) {
        var self = this;

        Object.keys(layout).forEach(function(rowId) {
            var row = document.getElementById(rowId);
            var widgets = layout[rowId];

            if (row && widgets && widgets.length) {
                // Create a temporary container to hold widgets
                var temp = document.createElement('div');

                // Move widgets to temp container in the correct order
                widgets.forEach(function(widgetId) {
                    var widget = document.querySelector('[data-widget-id="' + widgetId + '"]');
                    if (widget) {
                        temp.appendChild(widget);
                    }
                });

                // Move widgets back to the row
                while (temp.firstChild) {
                    row.appendChild(temp.firstChild);
                }
            }
        });

        // Reinitialize sortable after layout change
        this.initializeSortable();
    },

    // Reset to default layout
    resetLayout: function() {
        localStorage.removeItem('dashboard-layout');
        this.applyLayout(this.defaultLayout);

        // Show confirmation message
        this.showMessage('Dashboard layout has been reset to default!', 'success');

        console.log('Dashboard layout reset to default');
    },

    // Show temporary message
    showMessage: function(message, type) {
        var $message = $('<div class="dashboard-message alert alert-' + (type || 'info') + '">')
            .text(message)
            .css({
                position: 'fixed',
                top: '80px',
                right: '20px',
                zIndex: 9999,
                minWidth: '300px'
            });

        $('body').append($message);

        setTimeout(function() {
            $message.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
};

// Initialize drag and drop when document is ready
$(document).ready(function() {
    // Wait a bit for other scripts to load
    setTimeout(function() {
        dashboardDragDrop.init();
    }, 500);
});
</script>